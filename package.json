{"name": "joining_dots_user", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b", "scripts": {"dev": "next dev --turbopack", "build": "next build", "build-no-types": "NEXT_SKIP_TYPECHECKING=1 next build --no-lint", "start": "next start", "lint": "next lint", "pretty": "prettier --write .", "deploy": "vercel --prod"}, "dependencies": {"@hookform/resolvers": "^4.1.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.66.9", "@tanstack/react-query-devtools": "^5.66.9", "@tremor/react": "^3.18.7", "axios": "^1.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "cookies-next": "^5.1.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "geist": "^1.3.1", "input-otp": "^1.4.2", "lucide-react": "^0.476.0", "next": "15.1.7", "next-themes": "^0.4.4", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.7", "eslint-config-prettier": "^10.0.2", "lint-staged": "^15.4.3", "postcss": "^8", "prettier": "^3.5.2", "tailwindcss": "^3.4.1", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "*.{json,css,md}": ["prettier --write"]}}