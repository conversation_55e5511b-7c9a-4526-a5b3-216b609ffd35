'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useUser } from '@/hooks/auth';
import { socketIOService as PollSocketAPI } from '@/lib/socket-io';
import { getCookie } from 'cookies-next';
import { toast } from 'sonner';
import type {
  Poll,
  Question,
  ActiveQuestionData,
  QuestionEndedData,
} from '@/types/content';
import { PollQuestionComponent } from '@/components/polls/poll-question';

export default function PollParticipationPage() {
  const params = useParams();
  const router = useRouter();
  const { data: user } = useUser();
  const pollId = params.id as string;

  // Poll state
  const [poll, setPoll] = useState<Poll | null>(null);
  const [activeQuestion, setActiveQuestion] = useState<Question | null>(null);
  const [participants, setParticipants] = useState<number>(0);
  const [connected, setConnected] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [questionEnded, setQuestionEnded] = useState<boolean>(false);

  // Initialize WebSocket connection and event handlers
  useEffect(() => {
    if (!user) {
      toast.error('Please log in to participate in polls');
      router.push('/login');
      return;
    }

    const accessToken = getCookie('accessToken');
    if (!accessToken) {
      toast.error('Authentication required');
      router.push('/login');
      return;
    }

    // Set up event handlers for the centralized WebSocket service
    PollSocketAPI.setEventHandlers({
      onConnected: () => {
        console.log('✅ Connected to WebSocket server successfully');
        setConnected(true);
        setLoading(false);

        // Join the poll room
        console.log(`🔄 Joining poll: ${pollId}`);
        PollSocketAPI.joinPoll(pollId);
      },

      onDisconnected: () => {
        console.log('Disconnected from poll WebSocket');
        setConnected(false);
      },

      onActiveQuestion: (data: ActiveQuestionData) => {
        console.log('New active question:', data);
        setActiveQuestion(data.data.question);
        setQuestionEnded(false);

        // Reset any previous state when new question arrives
        toast.success('New question is now active!');
      },

      onPollUpdated: (data: {
        action: string;
        data: { question?: Question; count?: number; results?: unknown };
      }) => {
        console.log('Poll update:', data);

        switch (data.action) {
          case 'new-question':
            if (data.data.question) {
              setActiveQuestion(data.data.question);
              setQuestionEnded(false);
              toast.success('New question is now active!');
            }
            break;
          case 'participant-count-updated':
            setParticipants(data.data.count);
            break;
          case 'question-results':
            // Handle results display if needed
            console.log('Question results:', data.data.results);
            break;
          default:
            console.log('Unknown poll update action:', data.action);
        }
      },

      onQuestionEnded: (data: QuestionEndedData) => {
        console.log('Question ended:', data);
        setQuestionEnded(true);
        toast.info('Question has ended. Waiting for next question...');
      },

      onParticipantCountUpdated: (event: {
        data?: { count?: number };
        count?: number;
      }) => {
        // Handle the backend's event format: data.data.count or event.count
        const participantCount = event.data?.count || event.count;
        if (participantCount !== undefined) {
          setParticipants(participantCount);
          console.log(`Participant count updated: ${participantCount}`);
        }
      },

      onJoinedPoll: (event: {
        data?: { count?: number; pollId?: string; poll?: Poll };
        count?: number;
        poll?: Poll;
      }) => {
        console.log('Successfully joined poll:', event);

        // Handle the backend's event format: data.data.count
        const participantCount = event.data?.count || event.count || 0;
        setParticipants(participantCount);

        console.log(
          `Joined poll ${event.data?.pollId || pollId} with ${participantCount} participants`
        );
        toast.success(`Joined poll with ${participantCount} participants`);

        // If there's poll data in the response, use it
        if (event.poll || event.data?.poll) {
          setPoll(event.poll || event.data.poll);
        }
      },

      onError: (error: Event) => {
        console.error('❌ WebSocket error:', error);
        setConnected(false);
        setLoading(false);
        toast.error('Connection error. Poll will work in basic mode.');
      },

      onMessage: (event: unknown) => {
        console.log('📨 WebSocket message received:', event);
      },
    });

    // Initialize Socket.IO connection
    console.log('🚀 Initializing Socket.IO connection...');
    PollSocketAPI.initialize(accessToken.toString());

    // Cleanup on unmount
    return () => {
      PollSocketAPI.leavePoll(pollId);
      PollSocketAPI.disconnect();
    };
  }, [user, pollId, router]);

  // Handle leaving the poll
  const handleLeavePoll = () => {
    PollSocketAPI.leavePoll(pollId);
    PollSocketAPI.disconnect();
    router.push('/dashboard');
  };

  // Handle response submission
  const handleResponseSubmit = (answer: string | string[] | number) => {
    if (!activeQuestion) {
      toast.error('No active question to respond to');
      return;
    }

    PollSocketAPI.sendPollResponse(pollId, activeQuestion.id, answer);
    toast.success('Response submitted!');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#14C8C8] mx-auto mb-4"></div>
            <p>Connecting to poll...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Poll Header */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-2xl text-[#14C8C8]">
                  {poll?.title || 'Live Poll'}
                </CardTitle>
                <CardDescription>Poll ID: {pollId}</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={connected ? 'default' : 'destructive'}>
                  {connected ? 'Connected' : 'Disconnected'}
                </Badge>
                <Badge variant="outline">{participants} participants</Badge>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Question Display */}
        {activeQuestion && !questionEnded ? (
          <PollQuestionComponent
            question={activeQuestion}
            onSubmitResponse={handleResponseSubmit}
          />
        ) : (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="space-y-4">
                {questionEnded ? (
                  <>
                    <h3 className="text-xl font-semibold text-gray-700">
                      Question Ended
                    </h3>
                    <p className="text-gray-600">
                      Waiting for the next question...
                    </p>
                  </>
                ) : (
                  <>
                    <h3 className="text-xl font-semibold text-gray-700">
                      Waiting for Question
                    </h3>
                    <p className="text-gray-600">
                      The poll host will start the questions soon.
                    </p>
                  </>
                )}
                <div className="animate-pulse flex justify-center">
                  <div className="h-2 w-32 bg-gray-300 rounded"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Leave Poll Button */}
        <div className="text-center">
          <Button
            variant="outline"
            onClick={handleLeavePoll}
            className="text-red-600 border-red-600 hover:bg-red-50"
          >
            Leave Poll
          </Button>
        </div>
      </div>
    </div>
  );
}
