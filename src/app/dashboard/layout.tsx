'use client';

import { SiteHeader } from '@/components/site-header';
// import { SiteFooter } from '@/components/site-footer';
import { ScrollArea } from '@/components/ui/scroll-area';
import { DashboardSidebar } from '@/components/sidebar';
import { SidebarProvider } from '@/components/ui/sidebar';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { getCookie } from 'cookies-next';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();

  // Check if user is authenticated on the client side
  useEffect(() => {
    const accessToken = getCookie('accessToken');
    if (!accessToken) {
      router.push('/login');
    }
  }, [router]);

  return (
    <div className="relative min-h-screen">
      <SidebarProvider>
        <div className="flex min-h-screen w-full">
          <DashboardSidebar />
          <div className="flex flex-col flex-1 w-full min-w-0">
            <SiteHeader />
            <main className="flex-1 bg-muted/40 w-full">
              <ScrollArea className="h-[calc(100vh-4rem)]">
                <div className="px-3 py-4 sm:px-4 sm:py-6 md:px-6 lg:px-8">
                  {children}
                </div>
              </ScrollArea>
            </main>
            {/* <SiteFooter /> */}
          </div>
        </div>
      </SidebarProvider>
    </div>
  );
}
