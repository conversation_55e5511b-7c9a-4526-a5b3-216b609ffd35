'use client';

import Image from 'next/image';
import Link from 'next/link';
import {
  CalendarIcon,
  StarFilledIcon,
  ReloadIcon,
} from '@radix-ui/react-icons';
import { Zap } from 'lucide-react';
import { DonutChart } from '@/components/ui/chart';
import { useDashboard } from '@/hooks/dashboard';
import { useUser } from '@/hooks/auth';
import { getUserNameFromToken, generateUserInitials } from '@/lib/utils/token';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';

export default function DashboardPage() {
  const { data: dashboardData, loading, error, refetch } = useDashboard();
  const { data: user } = useUser();

  // Get user name from token as a fallback
  const tokenName = getUserNameFromToken();

  // Get user data from local storage as another fallback
  const storedUserData =
    typeof window !== 'undefined'
      ? JSON.parse(localStorage.getItem('userData') || 'null')
      : null;

  // Use real data only - no fallbacks
  const quizData = dashboardData?.quizScores || [];
  const courseProgress = dashboardData?.courseProgress || 0;
  const dailyStreak = dashboardData?.dailyStreak || 0;
  const highestQuizScore = dashboardData?.highestQuizScore || 0;
  const topPerformers = dashboardData?.topPerformers || [];
  const upcomingSessions = dashboardData?.upcomingSessions || [];

  if (loading) {
    return (
      <div className="h-full w-full p-4 md:p-6 space-y-6">
        {/* Skeleton for Dashboard Header */}
        <div className="bg-gradient-to-br from-primary/10 via-primary/5 to-accent/10 dark:from-primary/20 dark:via-primary/10 dark:to-accent/20 p-6 rounded-xl mb-6 border border-primary/20 dark:border-primary/30">
          <div className="mb-4 flex justify-between items-center">
            <Skeleton className="h-10 w-40" />
            <div className="flex items-center gap-3">
              <div className="text-right">
                <Skeleton className="h-5 w-24 mb-1" />
                <Skeleton className="h-4 w-32" />
              </div>
              <Skeleton className="h-10 w-10 rounded-full" />
            </div>
          </div>
          <Skeleton className="h-5 w-48 mb-4" />
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="flex items-end justify-center space-x-2">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex flex-col items-center">
                  <Skeleton className="w-10 h-20 rounded-t-md" />
                  <Skeleton className="w-10 h-4 mt-1" />
                </div>
              ))}
            </div>
            <Skeleton className="h-36 w-36 rounded-full" />
            <div className="flex-none flex space-x-20 items-center">
              <div className="text-center">
                <Skeleton className="h-10 w-24 mb-1" />
                <Skeleton className="h-8 w-20" />
              </div>
              <div className="text-center">
                <Skeleton className="h-10 w-24 mb-1" />
                <Skeleton className="h-8 w-20" />
              </div>
            </div>
          </div>
        </div>
        {/* Skeleton for rest of dashboard */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
          <div className="bg-card dark:bg-card p-4 md:p-6 rounded-xl shadow-sm border border-border lg:col-span-2">
            <Skeleton className="h-8 w-48 mb-4" />
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div
                  key={i}
                  className="flex items-center justify-between border-b pb-3"
                >
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div>
                      <Skeleton className="h-5 w-32 mb-1" />
                      <Skeleton className="h-4 w-40" />
                    </div>
                  </div>
                  <Skeleton className="h-5 w-16" />
                </div>
              ))}
            </div>
          </div>
          <div className="flex flex-col gap-4 md:gap-6">
            {/* Quick Actions Skeleton */}
            <div className="bg-gradient-to-br from-card to-card/80 dark:from-slate-900 dark:to-slate-800 p-6 rounded-xl shadow-lg dark:shadow-2xl border border-border/50 backdrop-blur-sm">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <Skeleton className="h-7 w-32 mb-2" />
                  <Skeleton className="h-4 w-40" />
                </div>
                <Skeleton className="h-10 w-10 rounded-xl" />
              </div>
              <div className="grid grid-cols-2 gap-4">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="p-6 bg-gradient-to-br from-primary/5 to-transparent dark:from-primary/10 dark:to-transparent rounded-xl border border-primary/10">
                    <Skeleton className="h-12 w-12 rounded-xl mx-auto mb-3" />
                    <Skeleton className="h-4 w-16 mx-auto" />
                  </div>
                ))}
              </div>
            </div>
            {/* Upcoming Sessions Skeleton */}
            <div className="bg-gradient-to-br from-card to-card/80 dark:from-slate-900 dark:to-slate-800 p-6 rounded-xl shadow-lg dark:shadow-2xl border border-border/50 backdrop-blur-sm">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <Skeleton className="h-7 w-40 mb-2" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <Skeleton className="h-8 w-20 rounded-lg" />
              </div>
              <div className="space-y-3">
                {[1, 2].map((i) => (
                  <div key={i} className="flex items-start gap-4 p-3 rounded-xl">
                    <Skeleton className="h-10 w-10 rounded-xl flex-shrink-0" />
                    <div className="flex-1">
                      <Skeleton className="h-5 w-32 mb-1" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6">
        <div className="text-red-500 mb-4 text-center">
          <p className="text-xl font-semibold mb-2">Error Loading Dashboard</p>
          <p>{error}</p>
        </div>
        <Button
          onClick={() => refetch()}
          variant="outline"
          className="flex items-center gap-2"
        >
          <ReloadIcon className="h-4 w-4" />
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="h-full w-full p-4 md:p-6 space-y-6">
      {/* Combined Dashboard Header and Quiz Score Section */}
      <div className="bg-gradient-to-br from-primary/10 via-primary/5 to-accent/10 dark:from-primary/20 dark:via-primary/10 dark:to-accent/20 p-6 rounded-xl mb-6 border border-primary/20 dark:border-primary/30 shadow-lg dark:shadow-primary/10">
        <div className="mb-4 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => refetch()}
              title="Refresh dashboard data"
              className="rounded-full h-8 w-8 hover:bg-primary/20 dark:hover:bg-primary/30 transition-colors"
            >
              <ReloadIcon className="h-4 w-4" />
            </Button>
          </div>
          {/* User info section with consistent display */}
          <div className="flex items-center gap-3">
            <div className="text-right">
              <p className="font-medium">
                {user?.name && user.name !== 'user'
                  ? user.name
                  : storedUserData?.name && storedUserData.name !== 'user'
                    ? storedUserData.name
                    : tokenName}
              </p>
              {dashboardData?.userBelt ? (
                <p className="text-sm text-gray-600">
                  {dashboardData.userBelt} Belt • {dashboardData.userXp || 0} XP
                </p>
              ) : (
                <p className="text-sm text-gray-600">
                  Dashboard • Personal View
                </p>
              )}
            </div>
            <div className="h-10 w-10 min-h-10 min-w-10 rounded-full bg-[#14C8C8] flex items-center justify-center text-white aspect-square">
              <span className="font-medium text-sm leading-none">
                {generateUserInitials(
                  user?.name && user.name !== 'user'
                    ? user.name
                    : storedUserData?.name && storedUserData.name !== 'user'
                      ? storedUserData.name
                      : tokenName
                )}
              </span>
            </div>
          </div>
        </div>

        <h2 className="text-base text-gray-600 mb-2">
          Your Previous Quiz Score
        </h2>

        <div className="flex flex-col md:flex-row items-center justify-between gap-4">
          {/* Left section - Quiz bars */}
          <div className="flex items-end justify-center space-x-2">
            {quizData.length > 0 ? (
              quizData.map((item, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div
                    className="w-10 bg-[#14C8C8] rounded-t-md"
                    style={{ height: `${item.score / 3}px` }}
                  ></div>
                  <p className="text-xs mt-1">{item.quiz}</p>
                </div>
              ))
            ) : (
              <div className="flex flex-col items-center justify-center h-24 w-full">
                <p className="text-sm text-gray-500">
                  No quiz data available yet
                </p>
                <p className="text-xs text-gray-400">
                  Complete quizzes to see your scores
                </p>
              </div>
            )}
          </div>

          {/* Center section - Course Progress */}
          <div className="flex-none flex flex-col items-center justify-center">
            <DonutChart
              value={courseProgress}
              size={150}
              thickness={12}
              showLabel={true}
            />
            <p className="text-sm text-center mt-2">Course Progress</p>
          </div>

          {/* Right section - Stats in a row */}
          <div className="flex-none flex space-x-20 items-center">
            {/* Daily Streak */}
            <div className="text-center">
              <h3 className="text-3xl font-bold text-[#14C8C8]">
                {dailyStreak} Days
              </h3>
              <p className="text-xs text-muted-foreground">
                Daily
                <br />
                Streak Counter
              </p>
            </div>

            {/* Highest Quiz Score */}
            <div className="text-center">
              <h3 className="text-3xl font-bold text-[#14C8C8]">
                {highestQuizScore} Pts
              </h3>
              <p className="text-xs text-muted-foreground">
                Highest
                <br />
                Quiz Score
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
        {/* Left column - Top Performers */}
        <div className="bg-card dark:bg-card p-4 md:p-6 rounded-xl shadow-sm border border-border lg:col-span-2">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-xl font-semibold">Top Performers</h2>
              <p className="text-sm text-muted-foreground">
                Top quiz champions revealed! 🏆
              </p>
            </div>
            <div className="rounded-full bg-muted p-2">
              <StarFilledIcon className="h-5 w-5 text-amber-500" />
            </div>
          </div>

          <div className="space-y-4">
            {topPerformers.length > 0 ? (
              topPerformers.map((performer) => (
                <div
                  key={performer.rank}
                  className="flex items-center justify-between border-b pb-3 last:border-0"
                >
                  <div className="flex items-center gap-3">
                    {performer.rank <= 3 ? (
                      <div className="flex items-center justify-center w-8 h-8">
                        <Image
                          src={`/icons/medal-${performer.rank}.svg`}
                          alt={`Rank ${performer.rank}`}
                          width={32}
                          height={32}
                        />
                      </div>
                    ) : (
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100">
                        <span className="text-sm font-medium">
                          {performer.rank}
                        </span>
                      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 min-h-8 min-w-8 rounded-full bg-gray-200 flex items-center justify-center text-xs aspect-square">
                        <span className="font-medium leading-none">
                          {generateUserInitials(performer.name)}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-sm">{performer.name}</p>
                        <p className="text-xs text-gray-500">
                          {performer.email}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="text-sm font-medium">
                    {performer.score} pts
                  </div>
                </div>
              ))
            ) : (
              <div className="flex flex-col items-center justify-center py-8">
                <p className="text-muted-foreground mb-2">
                  No leaderboard data available yet
                </p>
                <p className="text-sm text-muted-foreground/70">
                  Complete quizzes to appear on the leaderboard
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Right column - Actions */}
        <div className="flex flex-col gap-4 md:gap-6">
          {/* Quick Actions */}
          <div className="bg-gradient-to-br from-card to-card/80 dark:from-slate-900 dark:to-slate-800 p-6 rounded-xl shadow-lg dark:shadow-2xl border border-border/50 backdrop-blur-sm">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="font-bold text-lg bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">Quick Actions</h3>
                <p className="text-sm text-muted-foreground mt-1">Access your tools instantly</p>
              </div>
              <div className="bg-gradient-to-br from-amber-500/20 to-orange-500/20 p-2.5 rounded-xl">
                <Zap className="h-5 w-5 text-amber-500" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <Link
                href="/dashboard/sessions"
                className="group relative flex flex-col items-center justify-center p-6 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent dark:from-primary/20 dark:via-primary/10 dark:to-transparent rounded-xl hover:from-primary/20 hover:via-primary/10 hover:to-primary/5 dark:hover:from-primary/30 dark:hover:via-primary/20 dark:hover:to-primary/10 transition-all duration-300 border border-primary/20 hover:border-primary/40 shadow-sm hover:shadow-md hover:scale-105"
              >
                <div className="bg-gradient-to-br from-primary/20 to-primary/30 p-3 rounded-xl mb-3 group-hover:scale-110 transition-transform duration-300">
                  <Image
                    src="/icons/quiz-icon.svg"
                    alt="Sessions"
                    width={24}
                    height={24}
                    className="filter brightness-0 invert opacity-80"
                  />
                </div>
                <span className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">Sessions</span>
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl" />
              </Link>
              <Link
                href="/dashboard/contents"
                className="group relative flex flex-col items-center justify-center p-6 bg-gradient-to-br from-emerald-500/10 via-emerald-500/5 to-transparent dark:from-emerald-500/20 dark:via-emerald-500/10 dark:to-transparent rounded-xl hover:from-emerald-500/20 hover:via-emerald-500/10 hover:to-emerald-500/5 dark:hover:from-emerald-500/30 dark:hover:via-emerald-500/20 dark:hover:to-emerald-500/10 transition-all duration-300 border border-emerald-500/20 hover:border-emerald-500/40 shadow-sm hover:shadow-md hover:scale-105"
              >
                <div className="bg-gradient-to-br from-emerald-500/20 to-emerald-500/30 p-3 rounded-xl mb-3 group-hover:scale-110 transition-transform duration-300">
                  <Image
                    src="/icons/content-icon.svg"
                    alt="Resources"
                    width={24}
                    height={24}
                    className="filter brightness-0 invert opacity-80"
                  />
                </div>
                <span className="text-sm font-medium text-foreground group-hover:text-emerald-600 dark:group-hover:text-emerald-400 transition-colors">Resources</span>
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl" />
              </Link>
              <Link
                href="/join-poll"
                className="group relative flex flex-col items-center justify-center p-6 bg-gradient-to-br from-purple-500/10 via-purple-500/5 to-transparent dark:from-purple-500/20 dark:via-purple-500/10 dark:to-transparent rounded-xl hover:from-purple-500/20 hover:via-purple-500/10 hover:to-purple-500/5 dark:hover:from-purple-500/30 dark:hover:via-purple-500/20 dark:hover:to-purple-500/10 transition-all duration-300 border border-purple-500/20 hover:border-purple-500/40 shadow-sm hover:shadow-md hover:scale-105"
              >
                <div className="bg-gradient-to-br from-purple-500/20 to-purple-500/30 p-3 rounded-xl mb-3 group-hover:scale-110 transition-transform duration-300">
                  <Image
                    src="/icons/live-poll-icon.svg"
                    alt="Join Poll"
                    width={24}
                    height={24}
                    className="filter brightness-0 invert opacity-80"
                  />
                </div>
                <span className="text-sm font-medium text-foreground group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">Join Poll</span>
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl" />
              </Link>
              <Link
                href="/dashboard/leaderboard"
                className="group relative flex flex-col items-center justify-center p-6 bg-gradient-to-br from-amber-500/10 via-amber-500/5 to-transparent dark:from-amber-500/20 dark:via-amber-500/10 dark:to-transparent rounded-xl hover:from-amber-500/20 hover:via-amber-500/10 hover:to-amber-500/5 dark:hover:from-amber-500/30 dark:hover:via-amber-500/20 dark:hover:to-amber-500/10 transition-all duration-300 border border-amber-500/20 hover:border-amber-500/40 shadow-sm hover:shadow-md hover:scale-105"
              >
                <div className="bg-gradient-to-br from-amber-500/20 to-amber-500/30 p-3 rounded-xl mb-3 group-hover:scale-110 transition-transform duration-300">
                  <Image
                    src="/icons/leaderboard-icon.svg"
                    alt="Leaderboard"
                    width={24}
                    height={24}
                    className="filter brightness-0 invert opacity-80"
                  />
                </div>
                <span className="text-sm font-medium text-foreground group-hover:text-amber-600 dark:group-hover:text-amber-400 transition-colors">Leaderboard</span>
                <div className="absolute inset-0 bg-gradient-to-br from-amber-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl" />
              </Link>
            </div>
          </div>

          {/* Upcoming Sessions */}
          <div className="bg-gradient-to-br from-card to-card/80 dark:from-slate-900 dark:to-slate-800 p-6 rounded-xl shadow-lg dark:shadow-2xl border border-border/50 backdrop-blur-sm">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="font-bold text-lg bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">Upcoming Sessions</h3>
                <p className="text-sm text-muted-foreground mt-1">Your scheduled learning sessions</p>
              </div>
              <Link
                href="/dashboard/sessions"
                className="text-sm text-primary hover:text-primary/80 transition-colors font-medium bg-primary/10 dark:bg-primary/20 px-3 py-1.5 rounded-lg hover:bg-primary/20 dark:hover:bg-primary/30"
              >
                View all
              </Link>
            </div>
            <div className="space-y-3">
              {upcomingSessions.length > 0 ? (
                upcomingSessions.map((session, index) => (
                  <Link
                    href="/dashboard/sessions"
                    key={index}
                    className="group flex items-start gap-4 hover:bg-gradient-to-r hover:from-primary/5 hover:to-transparent dark:hover:from-primary/10 dark:hover:to-transparent p-3 rounded-xl transition-all duration-300 border border-transparent hover:border-primary/20 hover:shadow-sm"
                  >
                    <div className="bg-gradient-to-br from-primary/20 to-primary/30 p-2.5 rounded-xl group-hover:scale-110 transition-transform duration-300">
                      <CalendarIcon className="h-4 w-4 text-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-foreground group-hover:text-primary transition-colors truncate">{session.title}</p>
                      <p className="text-sm text-muted-foreground mt-0.5">{session.date}</p>
                    </div>
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="bg-primary/10 dark:bg-primary/20 p-1.5 rounded-lg">
                        <svg className="h-3 w-3 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </Link>
                ))
              ) : (
                <div className="flex flex-col items-center justify-center py-8 px-4">
                  <div className="bg-gradient-to-br from-muted/30 to-muted/10 dark:from-slate-800/30 dark:to-slate-700/10 p-4 rounded-full mb-4">
                    <CalendarIcon className="h-8 w-8 text-muted-foreground/60" />
                  </div>
                  <p className="text-muted-foreground text-sm font-medium mb-2">No upcoming sessions</p>
                  <p className="text-muted-foreground/70 text-xs text-center mb-4">Join a session to see it appear here</p>
                  <Link
                    href="/dashboard/sessions"
                    className="text-primary text-sm font-medium hover:text-primary/80 transition-colors bg-primary/10 dark:bg-primary/20 px-4 py-2 rounded-lg hover:bg-primary/20 dark:hover:bg-primary/30"
                  >
                    Browse all sessions
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
