import { axiosInstance } from '@/lib/axios';
import { toast } from 'sonner';
import {
  Content,
  ContentUploadRequest,
  ContentUpdateRequest,
  ContentListResponse,
  ContentResponse,
  ContentListParams,
  validateContentFile,
  getContentTypeFromFile,
} from '@/types/content';

// API Functions for Content Management

/**
 * Upload a new content file to a session
 */
export const uploadContent = async (
  request: ContentUploadRequest
): Promise<Content> => {
  try {
    // Validate the file before upload
    const validation = validateContentFile(request.file);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Auto-detect content type if not provided
    const contentType = request.type || getContentTypeFromFile(request.file);
    if (!contentType) {
      throw new Error('Unable to determine content type from file');
    }

    // Create FormData for file upload
    const formData = new FormData();
    formData.append('file', request.file);
    formData.append('title', request.title);
    formData.append('sessionId', request.sessionId);
    formData.append('type', contentType);

    console.log('Uploading content:', {
      fileName: request.file.name,
      fileSize: request.file.size,
      title: request.title,
      sessionId: request.sessionId,
      type: contentType,
    });

    const response = await axiosInstance.post<ContentResponse>(
      '/content',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        // Add upload progress tracking if needed
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            console.log(`Upload progress: ${percentCompleted}%`);
          }
        },
      }
    );

    console.log('Content uploaded successfully:', response.data);
    toast.success('Content uploaded successfully');
    return response.data.content;
  } catch (error: unknown) {
    console.error('Error uploading content:', error);
    const errorMessage =
      (
        error as {
          response?: { data?: { message?: string } };
          message?: string;
        }
      )?.response?.data?.message ||
      (error as { message?: string })?.message ||
      'Failed to upload content';
    toast.error(errorMessage);
    throw error;
  }
};

/**
 * Get content by ID
 */
export const fetchContentById = async (contentId: string): Promise<Content> => {
  try {
    console.log(`Fetching content with ID: ${contentId}`);
    const response = await axiosInstance.get<{ content: Content }>(
      `/content/${contentId}`
    );

    console.log('Content fetched successfully:', response.data);
    return response.data.content;
  } catch (error: unknown) {
    console.error('Error fetching content:', error);
    const errorMessage =
      (error as { response?: { data?: { message?: string } } })?.response?.data
        ?.message || 'Failed to fetch content';
    toast.error(errorMessage);
    throw error;
  }
};

/**
 * Get all content for a session with pagination and filtering
 */
export const fetchSessionContent = async (
  sessionId: string,
  params: ContentListParams = {}
): Promise<ContentListResponse> => {
  try {
    const { page = 1, limit = 10, type } = params;

    // Build query parameters
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (type) {
      queryParams.append('type', type);
    }

    const url = `/content/session/${sessionId}?${queryParams.toString()}`;
    console.log(`Fetching session content: ${url}`);

    const response = await axiosInstance.get<ContentListResponse>(url);

    console.log('Session content fetched successfully:', response.data);
    return response.data;
  } catch (error: unknown) {
    console.error('Error fetching session content:', error);
    const errorMessage =
      (error as { response?: { data?: { message?: string } } })?.response?.data
        ?.message || 'Failed to fetch session content';
    toast.error(errorMessage);
    throw error;
  }
};

/**
 * Update content metadata and permissions
 */
export const updateContent = async (
  contentId: string,
  request: ContentUpdateRequest
): Promise<Content> => {
  try {
    console.log(`Updating content ${contentId}:`, request);

    const response = await axiosInstance.put<ContentResponse>(
      `/content/${contentId}`,
      request
    );

    console.log('Content updated successfully:', response.data);
    toast.success('Content updated successfully');
    return response.data.content;
  } catch (error: unknown) {
    console.error('Error updating content:', error);
    const errorMessage =
      (error as { response?: { data?: { message?: string } } })?.response?.data
        ?.message || 'Failed to update content';
    toast.error(errorMessage);
    throw error;
  }
};

/**
 * Delete content
 */
export const deleteContent = async (contentId: string): Promise<void> => {
  try {
    console.log(`Deleting content with ID: ${contentId}`);

    await axiosInstance.delete(`/content/${contentId}`);

    console.log('Content deleted successfully');
    toast.success('Content deleted successfully');
  } catch (error: unknown) {
    console.error('Error deleting content:', error);
    const errorMessage =
      (error as { response?: { data?: { message?: string } } })?.response?.data
        ?.message || 'Failed to delete content';
    toast.error(errorMessage);
    throw error;
  }
};

/**
 * Download content file
 */
export const downloadContent = async (content: Content): Promise<void> => {
  try {
    console.log(`Downloading content: ${content.title}`);

    // Open the content URL in a new tab for download
    window.open(content.url, '_blank');

    toast.success('Download started');
  } catch (error: unknown) {
    console.error('Error downloading content:', error);
    toast.error('Failed to download content');
    throw error;
  }
};
