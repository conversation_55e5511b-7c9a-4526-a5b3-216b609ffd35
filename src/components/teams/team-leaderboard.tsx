'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Users,
  Target,
  Trophy,
  Medal,
  Award,
  Star,
  TrendingUp,
  ChevronDown,
  Crown,
  Zap,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { TeamLeaderboardResponse } from '@/types/teams';

interface TeamLeaderboardProps {
  data: TeamLeaderboardResponse;
  onSortChange?: (sortBy: string, order: 'asc' | 'desc') => void;
  userTeamId?: string;
  className?: string;
}

export function TeamLeaderboard({
  data,
  onSortChange,
  userTeamId,
  className,
}: TeamLeaderboardProps) {
  const [sortBy, setSortBy] = useState('totalQuizScore');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [expandedTeams, setExpandedTeams] = useState<Set<string>>(new Set());

  const sortOptions = [
    { value: 'totalQuizScore', label: 'Total Quiz Score' },
    { value: 'averageQuizScore', label: 'Average Quiz Score' },
    { value: 'totalQuizzesCompleted', label: 'Quizzes Completed' },
    { value: 'topContribution', label: 'Top Contributor' },
  ];

  const handleSortChange = (newSortBy: string) => {
    const newOrder = sortBy === newSortBy && sortOrder === 'desc' ? 'asc' : 'desc';
    setSortBy(newSortBy);
    setSortOrder(newOrder);
    onSortChange?.(newSortBy, newOrder);
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="h-3 w-3 sm:h-5 sm:w-5 text-yellow-500" />;
      case 2:
        return <Medal className="h-3 w-3 sm:h-5 sm:w-5 text-gray-400" />;
      case 3:
        return <Award className="h-3 w-3 sm:h-5 sm:w-5 text-amber-600" />;
      default:
        return <div className="w-3 h-3 sm:w-5 sm:h-5 rounded-full bg-muted flex items-center justify-center text-xs font-medium">{rank}</div>;
    }
  };



  const toggleTeamExpansion = (teamId: string) => {
    const newExpanded = new Set(expandedTeams);
    if (newExpanded.has(teamId)) {
      newExpanded.delete(teamId);
    } else {
      newExpanded.add(teamId);
    }
    setExpandedTeams(newExpanded);
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getTeamColor = (color?: string | null) => {
    if (!color) return 'bg-gradient-to-br from-blue-500 to-purple-600';
    
    const gradients = {
      blue: 'bg-gradient-to-br from-blue-500 to-purple-600',
      green: 'bg-gradient-to-br from-green-500 to-emerald-600',
      red: 'bg-gradient-to-br from-red-500 to-pink-600',
      yellow: 'bg-gradient-to-br from-yellow-500 to-orange-600',
      purple: 'bg-gradient-to-br from-purple-500 to-indigo-600',
      teal: 'bg-gradient-to-br from-teal-500 to-cyan-600',
    };
    
    return gradients[color as keyof typeof gradients] || gradients.blue;
  };

  return (
    <div className={cn('space-y-4 sm:space-y-6 px-3 sm:px-0', className)}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
        <div className="flex-1 min-w-0">
          <h2 className="text-lg sm:text-2xl font-bold truncate">Team Leaderboard</h2>
          <p className="text-muted-foreground text-sm sm:text-base truncate">
            {data.session.title} • {data.summary.totalTeams} teams competing
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Select value={sortBy} onValueChange={handleSortChange}>
            <SelectTrigger className="w-[140px] sm:w-[180px] text-xs sm:text-sm">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value} className="text-xs sm:text-sm">
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-4">
        <Card>
          <CardContent className="p-2 sm:p-4">
            <div className="flex items-center space-x-1 sm:space-x-2">
              <Users className="h-3 w-3 sm:h-4 sm:w-4 text-blue-500 flex-shrink-0" />
              <div className="min-w-0">
                <p className="text-xs sm:text-sm font-medium truncate">Total Teams</p>
                <p className="text-lg sm:text-2xl font-bold">{data.summary.totalTeams}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-2 sm:p-4">
            <div className="flex items-center space-x-1 sm:space-x-2">
              <Target className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 flex-shrink-0" />
              <div className="min-w-0">
                <p className="text-xs sm:text-sm font-medium truncate">Participants</p>
                <p className="text-lg sm:text-2xl font-bold">{data.summary.totalParticipants}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-2 sm:p-4">
            <div className="flex items-center space-x-1 sm:space-x-2">
              <Zap className="h-3 w-3 sm:h-4 sm:w-4 text-yellow-500 flex-shrink-0" />
              <div className="min-w-0">
                <p className="text-xs sm:text-sm font-medium truncate">Quizzes</p>
                <p className="text-lg sm:text-2xl font-bold">{data.summary.totalQuizzes}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-2 sm:p-4">
            <div className="flex items-center space-x-1 sm:space-x-2">
              <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-purple-500 flex-shrink-0" />
              <div className="min-w-0">
                <p className="text-xs sm:text-sm font-medium truncate">Total Score</p>
                <p className="text-lg sm:text-2xl font-bold">{data.summary.totalQuizPoints}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Leaderboard */}
      <div className="space-y-2 sm:space-y-3">
        {data.leaderboard.map((team) => {
          const isUserTeam = team.id === userTeamId;
          const isExpanded = expandedTeams.has(team.id);

          return (
            <Card
              key={team.id}
              className={cn(
                'transition-all duration-200 hover:shadow-md',
                isUserTeam && 'ring-2 ring-primary/50 shadow-lg',
                team.rank <= 3 && 'shadow-md'
              )}
            >
              <Collapsible>
                <CollapsibleTrigger
                  className="w-full"
                  onClick={() => toggleTeamExpansion(team.id)}
                >
                  <CardContent className="p-2 sm:p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0">
                        {/* Rank */}
                        <div className="flex items-center justify-center w-6 h-6 sm:w-10 sm:h-10 flex-shrink-0">
                          {getRankIcon(team.rank)}
                        </div>

                        {/* Team Info */}
                        <div className="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
                          <div className={cn('w-8 h-8 sm:w-12 sm:h-12 rounded-lg flex items-center justify-center text-white font-bold text-xs sm:text-base flex-shrink-0', getTeamColor(team.color))}>
                            {getInitials(team.name)}
                          </div>
                          <div className="text-left flex-1 min-w-0">
                            <div className="flex items-center space-x-1 sm:space-x-2">
                              <h3 className="font-semibold text-sm sm:text-lg truncate">{team.name}</h3>
                              {isUserTeam && (
                                <Badge className="bg-primary/10 text-primary text-xs flex-shrink-0">My Team</Badge>
                              )}
                            </div>
                            <p className="text-xs sm:text-sm text-muted-foreground">
                              {team.memberCount} {team.memberCount === 1 ? 'member' : 'members'}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Stats */}
                      <div className="flex items-center space-x-1 sm:space-x-6 flex-shrink-0">
                        <div className="text-center hidden sm:block">
                          <p className="text-sm text-muted-foreground">Total Score</p>
                          <p className="text-lg font-bold">{team.quizMetrics.totalQuizScore}</p>
                        </div>
                        <div className="text-center">
                          <p className="text-xs sm:text-sm text-muted-foreground">Score</p>
                          <p className="text-sm sm:text-lg font-bold">{Math.round(team.quizMetrics.averageQuizScore)}</p>
                        </div>
                        <div className="text-center hidden sm:block">
                          <p className="text-sm text-muted-foreground">Quizzes</p>
                          <p className="text-lg font-bold">{team.quizMetrics.totalQuizzesCompleted}</p>
                        </div>
                        <div className="text-center hidden lg:block">
                          <p className="text-sm text-muted-foreground">Top Contributor</p>
                          <p className="font-semibold">{team.quizMetrics.topContributor}</p>
                        </div>

                        <ChevronDown className={cn('h-3 w-3 sm:h-4 sm:w-4 transition-transform flex-shrink-0', isExpanded && 'rotate-180')} />
                      </div>
                    </div>
                  </CardContent>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <CardContent className="px-2 sm:px-4 pb-2 sm:pb-4 pt-0">
                    <div className="border-t pt-2 sm:pt-4">
                      <h4 className="font-semibold mb-2 sm:mb-3 flex items-center text-sm sm:text-base">
                          <Users className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                        Team Members ({team.members.length})
                        </h4>
                        <div className="grid grid-cols-1 gap-2 sm:gap-3">
                          {team.members.map((member) => (
                          <div key={member.id} className="flex items-center space-x-2 sm:space-x-3 p-2 sm:p-3 rounded-lg bg-muted/50">
                              <Avatar className="h-6 w-6 sm:h-8 sm:w-8 flex-shrink-0">
                                <AvatarImage src={member.user.profilePhoto || undefined} />
                                <AvatarFallback className="text-xs">
                                  {getInitials(member.user.name)}
                                </AvatarFallback>
                              </Avatar>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-1 sm:space-x-2">
                                  <p className="font-medium text-xs sm:text-sm truncate">{member.user.name}</p>
                                  {member.role === 'LEADER' && (
                                    <Crown className="h-3 w-3 text-yellow-500 flex-shrink-0" />
                                  )}
                              </div>
                              {member.quizContribution && (
                                <div className="flex items-center space-x-1">
                                  <Star className="h-3 w-3 text-yellow-500 flex-shrink-0" />
                                  <p className="text-xs sm:text-sm font-medium truncate">{member.quizContribution.totalQuizScore} total quiz score</p>
                                </div>
                              )}
                            </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          );
        })}
      </div>
      
      {data.leaderboard.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center">
            <Trophy className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
            <h3 className="font-semibold mb-2">No Teams Yet</h3>
            <p className="text-muted-foreground">Teams will appear here once they join the session.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 