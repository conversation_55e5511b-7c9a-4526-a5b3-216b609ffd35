/** @type {import('next').NextConfig} */
const nextConfig = {
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  async rewrites() {
    const apiBaseUrl =
      process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api';
    return [
      // Proxy specific backend routes, excluding local API routes
      {
        source: '/api/auth/:path*',
        destination: apiBaseUrl + '/auth/:path*',
      },
      {
        source: '/api/sessions/:path*',
        destination: apiBaseUrl + '/sessions/:path*',
      },
      {
        source: '/api/quizzes/:path*',
        destination: apiBaseUrl + '/quizzes/:path*',
      },
      {
        source: '/api/polls/:path*',
        destination: apiBaseUrl + '/polls/:path*',
      },
      {
        source: '/api/dashboard/:path*',
        destination: apiBaseUrl + '/dashboard/:path*',
      },
      {
        source: '/api/content/:path*',
        destination: apiBaseUrl + '/content/:path*',
      },
      // All content routes will now be proxied to the backend
    ];
  },
  // Ensure trailing slashes are handled correctly
  trailingSlash: false,
  // Configure output for better static site generation
  output: 'standalone',
};

module.exports = nextConfig;
